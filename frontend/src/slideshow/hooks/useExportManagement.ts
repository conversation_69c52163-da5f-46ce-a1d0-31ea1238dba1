import { useCallback } from 'react';
import { TimelineItem, ImageFile, ExportSettings, ExportResponse } from '../types/slideshow.types';

const API_BASE_URL = window.location.hostname === 'localhost' 
  ? 'http://localhost:3001' 
  : window.location.origin;

// Función para manejar la exportación con polling si es necesario
const handleExportResponse = async (
  result: any, 
  onProgress?: (progress: number, message: string) => void
): Promise<ExportResponse> => {
  // Si hay jobId y statusUrl, necesitamos hacer polling
  if (result.jobId && result.statusUrl) {
    console.log('🔄 Trabajo en cola, iniciando polling:', result.jobId);
    
    // Polling de estado
    let status = 'queued';
    let progress = 0;
    
    while (status !== 'completed' && status !== 'failed') {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Esperar 1 segundo
      
      try {
        const statusResponse = await fetch(`${API_BASE_URL}${result.statusUrl}`);
        if (!statusResponse.ok) throw new Error('Failed to get job status');
        
        const statusData = await statusResponse.json();
        status = statusData.status;
        progress = statusData.progress || 0;
        
        // Actualizar UI con progreso
        if (onProgress) onProgress(progress, statusData.message || 'Procesando...');
        
        if (status === 'failed') {
          throw new Error(statusData.error || 'Export failed');
        }
      } catch (error) {
        console.error('Error polling job status:', error);
        throw new Error('Failed to get export status');
      }
    }
    
    // Trabajo completado, devolver resultado final
    return {
      success: true,
      downloadUrl: result.downloadUrl,
      jobId: result.jobId
    };
  }
  
  // Procesamiento directo, devolver resultado tal cual
  return result;
};

// Función principal de exportación
const exportAPI = async (
  format: string, 
  payload: any, 
  onProgress?: (progress: number, message: string) => void
): Promise<ExportResponse> => {
  // Determinar endpoint
  let endpoint: string;
  if (format === 'gif') {
    endpoint = '/gif-simple';
  } else if (format === 'mp4' || format === 'webm' || format === 'mov') {
    endpoint = '/video-simple';
  } else {
    endpoint = `/api/unified-export/${format}`;
  }
  
  try {
    // Realizar petición
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // Manejar respuesta (con polling si es necesario)
    return handleExportResponse(result, onProgress);
  } catch (error) {
    console.error('Export API error:', error);
    throw error;
  }
};

export interface ExportState {
  isExporting: boolean;
  progress: number;
  lastResult: string | null;
  error: string | null;
  currentStep?: string;
}

export interface ExportActions {
  exportSlideshow: () => Promise<void>;
  updateExportSettings: (updates: Partial<ExportSettings>) => void;
}

export interface UseExportManagementProps {
  timeline: TimelineItem[];
  images: ImageFile[];
  sessionId?: string;
  exportSettings: ExportSettings;
  updateExportState: (updates: Partial<ExportState>) => void;
  updateExportSettingsState: (updates: Partial<ExportSettings>) => void;
}

export const useExportManagement = ({
  timeline,
  images,
  sessionId,
  exportSettings,
  updateExportState,
  updateExportSettingsState
}: UseExportManagementProps): ExportActions => {

  const simulateProgress = useCallback(() => {
    const steps = [
      { progress: 10, message: 'Preparing images...' },
      { progress: 25, message: 'Processing transitions...' },
      { progress: 45, message: 'Encoding frames...' },
      { progress: 70, message: 'Optimizing output...' },
      { progress: 90, message: 'Finalizing export...' }
    ];

    let stepIndex = 0;
    const progressInterval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex];
        updateExportState({
          progress: step.progress,
          currentStep: step.message
        });
        stepIndex++;
      } else {
        clearInterval(progressInterval);
      }
    }, 800);

    return progressInterval;
  }, [updateExportState]);

  const exportSlideshow = useCallback(async () => {
    if (timeline.length === 0) return;

    updateExportState({ 
      isExporting: true, 
      error: null, 
      progress: 0 
    });

    const progressInterval = simulateProgress();

    try {
      const payload = {
        images: timeline.map(item => {
          const image = images.find(img => img.id === item.imageId);
          return { filename: image?.uploadedInfo?.filename || image?.name };
        }),
        transitions: timeline.slice(0, -1).map(item => ({
          type: item.transition?.type || 'cut', // Default to 'cut' (no transition)
          duration: item.transition?.duration || 0 // No duration for cuts
        })),
        frameDurations: timeline.map(item => item.duration),
        sessionId: sessionId,
        // Flatten exportSettings for simple endpoints
        format: exportSettings.format,
        fps: exportSettings.fps,
        quality: exportSettings.quality,
        resolution: exportSettings.resolution?.preset || 'auto',
        videoConfig: exportSettings.resolution?.preset === 'custom' ? {
          resolution: {
            width: exportSettings.resolution?.width || 1920,
            height: exportSettings.resolution?.height || 1080
          }
        } : undefined
      };

      // Debug logging
      console.log('🎬 Export payload:', {
        totalImages: images.length,
        timelineItems: timeline.length,
        payload: payload,
        imageFilenames: payload.images.map(img => img.filename)
      });
      console.log('🎬 Full export payload:', JSON.stringify(payload, null, 2));

      // Usa el formato real, no 'video'
      const format = exportSettings.format; // 'mp4', 'webm', 'mov', 'gif'
      const result = await exportAPI(format, payload);
      
      clearInterval(progressInterval);
      
      if (result.success) {
        // Para el sistema unificado, necesitamos hacer polling del job
        updateExportState({
          progress: 80,
          currentStep: 'Processing export...'
        });

        // Poll job status until completion
        const pollJobStatus = async (jobId: string, maxAttempts = 30) => {
          for (let i = 0; i < maxAttempts; i++) {
            try {
              const statusResponse = await fetch(`${API_BASE_URL}/api/export/status/${jobId}`);
              if (statusResponse.ok) {
                const statusData = await statusResponse.json();
                const job = statusData.job;
                
                updateExportState({
                  progress: 80 + (job.progress * 0.2), // Scale progress from 80-100%
                  currentStep: job.status === 'active' ? 'Processing export...' : 'Finalizing...'
                });
                
                if (job.status === 'completed') {
                  // Job completed, now we can download
                  updateExportState({
                    progress: 100,
                    currentStep: 'Export complete!'
                  });

                  // Small delay to show completion, then trigger download
                  setTimeout(() => {
                    const link = document.createElement('a');
                    link.href = `${API_BASE_URL}${result.downloadUrl}`;
                    link.download = job.result.filename;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    updateExportState({
                      isExporting: false,
                      progress: 100,
                      lastResult: result.downloadUrl,
                      error: null,
                      currentStep: undefined
                    });
                  }, 1000);
                  return;
                } else if (job.status === 'failed') {
                  throw new Error(`Export failed: ${job.failedReason || 'Unknown error'}`);
                }
              }
            } catch (error) {
              console.error('❌ Status check failed:', error);
            }
            
            // Wait 1 second before next poll
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          
          throw new Error('Export timed out - job did not complete in time');
        };

        // Handle response type - direct or job-based
        if (result.downloadUrl) {
          // Direct response from simple endpoints
          updateExportState({
            isExporting: false,
            progress: 100,
            lastResult: result.downloadUrl,
            currentStep: undefined
          });
          console.log('✅ Direct export completed:', result.downloadUrl);
        } else if (result.jobId) {
          // Job-based response - start polling
          await pollJobStatus(result.jobId);
        } else {
          throw new Error('Invalid response format - no downloadUrl or jobId received');
        }
      }
    } catch (error) {
      clearInterval(progressInterval);
      console.error('❌ Export failed:', error);
      updateExportState({
        isExporting: false,
        error: error instanceof Error ? error.message : 'Export failed',
        currentStep: undefined
      });
    }
  }, [timeline, images, sessionId, exportSettings, updateExportState, simulateProgress]);

  const updateExportSettings = useCallback((updates: Partial<ExportSettings>) => {
    updateExportSettingsState(updates);
  }, [updateExportSettingsState]);

  return {
    exportSlideshow,
    updateExportSettings
  };
};
