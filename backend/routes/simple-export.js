const express = require('express');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Simple video export using native FFmpeg
router.post('/video-native', async (req, res) => {
  try {
    const { images, settings } = req.body;
    
    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Images array is required'
      });
    }

    const outputDir = path.join(__dirname, '../temp');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const outputFilename = `video_${uuidv4()}.mp4`;
    const outputPath = path.join(outputDir, outputFilename);

    // Create FFmpeg command
    const command = ffmpeg();
    
    // Add images as inputs
    images.forEach((imageData, index) => {
      const imageBuffer = Buffer.from(imageData.replace(/^data:image\/\w+;base64,/, ''), 'base64');
      const tempImagePath = path.join(outputDir, `temp_${index}.png`);
      fs.writeFileSync(tempImagePath, imageBuffer);
      command.input(tempImagePath);
    });

    // Configure output
    command
      .outputOptions([
        '-filter_complex', `concat=n=${images.length}:v=1:a=0,fps=${settings.fps || 24}`,
        '-pix_fmt', 'yuv420p'
      ])
      .output(outputPath);

    // Execute
    command.on('end', () => {
      // Clean up temp files
      images.forEach((_, index) => {
        const tempImagePath = path.join(outputDir, `temp_${index}.png`);
        if (fs.existsSync(tempImagePath)) {
          fs.unlinkSync(tempImagePath);
        }
      });

      res.download(outputPath, outputFilename, (err) => {
        if (fs.existsSync(outputPath)) {
          fs.unlinkSync(outputPath);
        }
      });
    });

    command.on('error', (err) => {
      console.error('FFmpeg error:', err);
      res.status(500).json({
        success: false,
        message: 'Video generation failed',
        error: err.message
      });
    });

    command.run();

  } catch (error) {
    console.error('Export error:', error);
    res.status(500).json({
      success: false,
      message: 'Export failed',
      error: error.message
    });
  }
});

module.exports = router;
